import * as React from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";

export interface Account {
  id: string;
  name: string;
  type: string;
}

export interface Vendor {
  id: string;
  name: string;
}

interface AccountFilterProps {
  accounts: Account[];
  selectedAccountId?: string;
  onAccountChange?: (accountId: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  getAccountTypeLabel?: (type: string) => string;
}

export function AccountFilter({
  accounts,
  selectedAccountId,
  onAccountChange,
  placeholder = "Select account",
  disabled = false,
  className,
  getAccountTypeLabel = (type) => type,
}: AccountFilterProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedAccount = accounts.find((account) => account.id === selectedAccountId);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAccountChange?.(undefined);
  };

  const handleSelect = (accountId: string) => {
    if (accountId === selectedAccountId) {
      onAccountChange?.(undefined);
    } else {
      onAccountChange?.(accountId);
    }
    setIsOpen(false);
  };

  const filteredAccounts = accounts.filter(
    (account) =>
      account.name.toLowerCase().includes(searchValue.toLowerCase()) || getAccountTypeLabel(account.type).toLowerCase().includes(searchValue.toLowerCase())
  );

  const displayText = selectedAccount ? `${selectedAccount.name} (${getAccountTypeLabel(selectedAccount.type)})` : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn("w-full justify-between font-normal", !selectedAccount && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <span className="flex-1 truncate text-left">{displayText}</span>
          <div className="flex items-center gap-1">
            {selectedAccount && <X className="h-4 w-4 hover:text-destructive" onClick={handleClear} />}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search accounts..." value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="pl-8" />
          </div>
        </div>
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {filteredAccounts.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">No accounts found.</div>
            ) : (
              filteredAccounts.map((account) => (
                <div
                  key={account.id}
                  className={cn(
                    "flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                    selectedAccountId === account.id && "bg-accent"
                  )}
                  onClick={() => handleSelect(account.id)}
                >
                  <Check className={cn("h-4 w-4", selectedAccountId === account.id ? "opacity-100" : "opacity-0")} />
                  <div className="flex-1">
                    <div className="font-medium">{account.name}</div>
                    <div className="text-xs text-muted-foreground">{getAccountTypeLabel(account.type)}</div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}

interface VendorFilterProps {
  vendors: Vendor[];
  selectedVendorId?: string;
  onVendorChange?: (vendorId: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function VendorFilter({ vendors, selectedVendorId, onVendorChange, placeholder = "Select vendor", disabled = false, className }: VendorFilterProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedVendor = vendors.find((vendor) => vendor.id === selectedVendorId);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onVendorChange?.(undefined);
  };

  const handleSelect = (vendorId: string) => {
    if (vendorId === selectedVendorId) {
      onVendorChange?.(undefined);
    } else {
      onVendorChange?.(vendorId);
    }
    setIsOpen(false);
  };

  const filteredVendors = vendors.filter((vendor) => vendor.name.toLowerCase().includes(searchValue.toLowerCase()));

  const displayText = selectedVendor ? selectedVendor.name : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn("w-full justify-between font-normal", !selectedVendor && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <div className="flex items-center justify-between w-full">
            <span className="truncate">{displayText}</span>
            {selectedVendor && <X className="h-4 w-4 shrink-0 opacity-50 hover:opacity-100 ml-2" onClick={handleClear} />}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search vendors..." value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="pl-8" />
          </div>
        </div>
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {filteredVendors.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">No vendors found.</div>
            ) : (
              filteredVendors.map((vendor) => (
                <div
                  key={vendor.id}
                  className={cn(
                    "flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                    selectedVendorId === vendor.id && "bg-accent"
                  )}
                  onClick={() => handleSelect(vendor.id)}
                >
                  <Check className={cn("h-4 w-4", selectedVendorId === vendor.id ? "opacity-100" : "opacity-0")} />
                  <div className="flex-1">
                    <div className="font-medium">{vendor.name}</div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
